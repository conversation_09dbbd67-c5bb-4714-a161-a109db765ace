/**
 * Date formatting utilities for displaying UTC datetime data in user's local timezone
 */

/**
 * Format options for date formatting
 */
export type DateFormatOptions = {
  /** Include time in the formatted output */
  includeTime?: boolean;
  /** Format as time only (no date) */
  timeOnly?: boolean;
  /** Use short month format (e.g., "Jan" instead of "January") */
  shortMonth?: boolean;
  /** Include seconds in time format */
  includeSeconds?: boolean;
  /** Use 24-hour format instead of 12-hour format */
  use24Hour?: boolean;
  /** Custom locale for formatting (defaults to user's locale) */
  locale?: string;
  /** Override to display in specific timezone (defaults to user's local timezone) */
  timezone?: string;
  /** Show timezone abbreviation in the output */
  showTimezone?: boolean;
};

/**
 * Default format options - defaults to user's local timezone
 */
const DEFAULT_FORMAT_OPTIONS: DateFormatOptions = {
  includeTime: false,
  timeOnly: false,
  shortMonth: true,
  includeSeconds: false,
  use24Hour: false,
  showTimezone: false,
  // timezone is intentionally undefined to use user's local timezone by default
};

/**
 * Common timezone identifiers for convenience
 */
export const TIMEZONES = {
  UTC: 'UTC',
  EST: 'America/New_York',
  PST: 'America/Los_Angeles',
  CST: 'America/Chicago',
  MST: 'America/Denver',
  GMT: 'Europe/London',
  CET: 'Europe/Paris',
  JST: 'Asia/Tokyo',
  IST: 'Asia/Kolkata',
  AEST: 'Australia/Sydney',
} as const;

/**
 * Validates if a timezone identifier is valid
 */
export function isValidTimezone(timezone: string): boolean {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch {
    return false;
  }
}

/**
 * Gets the user's current timezone
 */
export function getUserTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Formats a UTC datetime string into user's local timezone or specified timezone
 * 
 * @param utcDateInput - UTC date string, Date object, or null/undefined
 * @param options - Formatting options
 * @returns Formatted date string in user's local timezone or fallback text for invalid/null dates
 * 
 * @example
 * // UTC datetime displayed in user's local timezone
 * formatDate("2023-05-15T14:30:00Z") // "May 15, 2023" (in user's timezone)
 * 
 * @example
 * // UTC datetime with time in user's local timezone
 * formatDate("2023-05-15T14:30:00Z", { includeTime: true }) 
 * // If user is in EST: "May 15, 2023, 10:30 AM"
 * // If user is in JST: "May 16, 2023, 11:30 PM"
 * 
 * @example
 * // Time only in user's local timezone
 * formatDate("2023-05-15T14:30:00Z", { timeOnly: true }) 
 * // "10:30 AM" (EST) or "11:30 PM" (JST)
 * 
 * @example
 * // Show timezone abbreviation
 * formatDate("2023-05-15T14:30:00Z", { 
 *   includeTime: true, 
 *   showTimezone: true 
 * }) // "May 15, 2023, 10:30 AM EDT"
 * 
 * @example
 * // Override to specific timezone (useful for admin views)
 * formatDate("2023-05-15T14:30:00Z", { 
 *   includeTime: true,
 *   timezone: "UTC",
 *   showTimezone: true
 * }) // "May 15, 2023, 2:30 PM UTC"
 */
export function formatDate(
  utcDateInput: string | Date | null | undefined,
  options: DateFormatOptions = {}
): string {
  // Handle null, undefined or empty string
  if (utcDateInput === null || utcDateInput === undefined || utcDateInput === "") {
    return "N/A";
  }

  // Merge default options with provided options
  const mergedOptions = { ...DEFAULT_FORMAT_OPTIONS, ...options };
  const {
    includeTime,
    timeOnly,
    shortMonth,
    includeSeconds,
    use24Hour,
    locale,
    timezone,
    showTimezone,
  } = mergedOptions;

  try {
    // Parse the UTC date - handle both string and Date object inputs
    let date: Date;

    if (typeof utcDateInput === "string") {
      // Ensure the string is treated as UTC
      let utcString = utcDateInput;

      // If it's an ISO-like string without timezone, ensure it's treated as UTC
      if (utcString.includes('T') && !utcString.endsWith('Z') && !utcString.includes('+') && !utcString.includes('-', 10)) {
        utcString = utcString + 'Z';
      }

      date = new Date(utcString);
    } else {
      date = utcDateInput;
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "Invalid date";
    }

    // Validate timezone if provided
    if (timezone && !isValidTimezone(timezone)) {
      console.warn(`Invalid timezone: ${timezone}, using user's local timezone`);
    }

    // Use specified timezone or fall back to user's local timezone
    const displayTimezone = timezone && isValidTimezone(timezone) ? timezone : undefined;

    console.log("displayTimezone:", displayTimezone);

    // Common options for Intl formatters
    const baseOptions: Intl.DateTimeFormatOptions = {
      timeZone: displayTimezone, // undefined means user's local timezone
    };

    console.log("baseOptions:", baseOptions);

    // Format time only if requested
    if (timeOnly) {
      const timeOptions: Intl.DateTimeFormatOptions = {
        ...baseOptions,
        hour: "2-digit",
        minute: "2-digit",
        second: includeSeconds ? "2-digit" : undefined,
        hour12: !use24Hour,
        timeZoneName: showTimezone ? "short" : undefined,
      };

      return date.toLocaleTimeString(locale, timeOptions);
    }

    console.log("User timezone:", getUserTimezone()); // e.g., "America/New_York"

    // Format date part
    const dateOptions: Intl.DateTimeFormatOptions = {
      ...baseOptions,
      year: "numeric",
      month: shortMonth ? "short" : "long",
      day: "numeric",
    };

    const datePart = date.toLocaleDateString(locale, dateOptions);

    // Format time part if requested
    if (includeTime) {
      const timeOptions: Intl.DateTimeFormatOptions = {
        ...baseOptions,
        hour: "2-digit",
        minute: "2-digit",
        second: includeSeconds ? "2-digit" : undefined,
        hour12: !use24Hour,
        timeZoneName: showTimezone ? "short" : undefined,
      };

      const timePart = date.toLocaleTimeString(locale, timeOptions);
      return `${datePart}, ${timePart}`;
    }

    return datePart;
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Error formatting date";
  }
}

/**
 * Formats a UTC datetime to show the relative time difference (e.g., "2 hours ago", "in 3 days")
 * in user's local timezone context
 */
export function formatRelativeDate(
  utcDateInput: string | Date | null | undefined,
  options: { locale?: string } = {}
): string {
  if (utcDateInput === null || utcDateInput === undefined || utcDateInput === "") {
    return "N/A";
  }

  try {
    let date: Date;

    if (typeof utcDateInput === "string") {
      let utcString = utcDateInput;
      if (utcString.includes('T') && !utcString.endsWith('Z') && !utcString.includes('+') && !utcString.includes('-', 10)) {
        utcString = utcString + 'Z';
      }
      date = new Date(utcString);
    } else {
      date = utcDateInput;
    }

    if (isNaN(date.getTime())) {
      return "Invalid date";
    }

    // Calculate difference from current time
    const now = new Date();
    const diff = date.getTime() - now.getTime();

    // Use Intl.RelativeTimeFormat for localized relative time
    const rtf = new Intl.RelativeTimeFormat(options.locale, { numeric: 'auto' });

    const absDiff = Math.abs(diff);
    const seconds = Math.floor(absDiff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (years > 0) {
      return rtf.format(diff < 0 ? -years : years, 'year');
    } else if (months > 0) {
      return rtf.format(diff < 0 ? -months : months, 'month');
    } else if (weeks > 0) {
      return rtf.format(diff < 0 ? -weeks : weeks, 'week');
    } else if (days > 0) {
      return rtf.format(diff < 0 ? -days : days, 'day');
    } else if (hours > 0) {
      return rtf.format(diff < 0 ? -hours : hours, 'hour');
    } else if (minutes > 0) {
      return rtf.format(diff < 0 ? -minutes : minutes, 'minute');
    } else {
      return rtf.format(diff < 0 ? -seconds : seconds, 'second');
    }
  } catch (error) {
    console.error("Error formatting relative date:", error);
    return "Error formatting date";
  }
}

/**
 * Displays a UTC datetime in a specific timezone (useful for admin interfaces or multi-timezone apps)
 */
export function displayInTimezone(
  utcDateInput: string | Date | null | undefined,
  targetTimezone: string,
  options: Omit<DateFormatOptions, 'timezone'> = {}
): string {
  if (!isValidTimezone(targetTimezone)) {
    console.error(`Invalid timezone: ${targetTimezone}`);
    return "Invalid timezone";
  }

  return formatDate(utcDateInput, { ...options, timezone: targetTimezone });
}