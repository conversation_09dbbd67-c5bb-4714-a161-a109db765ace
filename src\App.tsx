import { Route, Routes, Navigate } from "react-router-dom";
import AuthLayout from "./layouts/AuthLayout";
import DashboardLayout from "./layouts/DashboardLayout";
import AuthGuard from "./components/AuthGuard";
import { Toaster } from "./components/Toaster";
import LoadingProvider from "./components/LoadingProvider";
import { PermissionProvider } from "./contexts/PermissionContext";
import PermissionRoute from "./components/PermissionRoute";
import { PERMISSIONS } from "./types/permissions";

// Auth Pages
import Login from "./pages/auth/Login";
import SignUp from "./pages/auth/SignUp";

// Dashboard Pages
import Dashboard from "./pages/dashboard/Dashboard";
import ScanFindings from "./pages/dashboard/ScanFindings";
import Scans from "./pages/dashboard/Scans";
import ScanDetails from "./pages/dashboard/ScanDetails";
import FindingDetail from "./pages/dashboard/FindingDetail";
import UserManagement from "./pages/dashboard/UserManagement";
import UserProfile from "./pages/dashboard/UserProfile";
import NotFound from "./pages/NotFound";
import AccountDetails from "./pages/dashboard/AccountDetails";

function App() {
  return (
    <LoadingProvider>
      <Routes>
        {/* Public Auth Routes */}
        <Route
          element={
            <AuthGuard requireAuth={false}>
              <AuthLayout />
            </AuthGuard>
          }
        >
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<SignUp />} />
        </Route>

        {/* Protected Dashboard Routes */}
        <Route
          element={
            <AuthGuard>
              <PermissionProvider>
                <DashboardLayout />
              </PermissionProvider>
            </AuthGuard>
          }
        >
          {/* Dashboard Home */}
          <Route path="/dashboard" element={<Dashboard />} />

          {/* User Profile - Accessible to all authenticated users */}
          <Route path="/dashboard/profile" element={<UserProfile />} />

          {/* Account Routes - Requires account management permissions */}
          <Route
            element={
              <PermissionRoute
                permissions={[
                  PERMISSIONS.CREATE_ACCOUNT,
                  PERMISSIONS.READ_ONLY,
                ]}
                anyPermission={true}
              />
            }
          >
            <Route
              path="/dashboard/accounts/:accountId"
              element={<AccountDetails />}
            />
          </Route>

          {/* Scan Routes - Requires scan permissions */}
          <Route
            element={
              <PermissionRoute
                permissions={[PERMISSIONS.SCAN_SERVICE, PERMISSIONS.READ_ONLY]}
                anyPermission={true}
              />
            }
          >
            <Route path="/dashboard/scans">
              <Route index element={<Scans />} />
              <Route path=":scanId">
                <Route index element={<ScanDetails />} />
                <Route path="findings">
                  <Route index element={<ScanFindings />} />
                  <Route path=":findingId" element={<FindingDetail />} />
                </Route>
              </Route>
            </Route>
          </Route>

          {/* User Management - Requires user management permissions */}
          <Route
            element={
              <PermissionRoute
                permissions={[
                  PERMISSIONS.CREATE_USER,
                  PERMISSIONS.DELETE_USER,
                  PERMISSIONS.UPDATE_USER_PERMISSIONS,
                  PERMISSIONS.ASSIGN_ROLE_TO_USER,
                  PERMISSIONS.REVOKE_ROLE_FROM_USER,
                  PERMISSIONS.CREATE_CUSTOM_ROLE,
                  PERMISSIONS.UPDATE_CUSTOM_ROLE,
                  PERMISSIONS.DELETE_CUSTOM_ROLE,
                ]}
                anyPermission={true}
              />
            }
          >
            <Route path="/dashboard/users" element={<UserManagement />} />
          </Route>
        </Route>

        {/* Redirects */}
        <Route
          path="/"
          element={
            <AuthGuard>
              <Navigate to="/dashboard" replace />
            </AuthGuard>
          }
        />

        {/* 404 Not Found Route - Add this at the end */}
        <Route path="*" element={<NotFound />} />
      </Routes>
      <Toaster />
    </LoadingProvider>
  );
}

export default App;
