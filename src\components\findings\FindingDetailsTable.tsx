import React from "react";
import { FindingDetailsTableProps } from "./types";
import FindingDetailsRow from "./FindingDetailsRow";

/**
 * FindingDetailsTable component renders a table of finding details
 * with proper handling of empty states and accessibility
 */
const FindingDetailsTable: React.FC<FindingDetailsTableProps> = ({
  finding,
  onRemediate,
  remediatingDetailIndex,
  isPending,
  onViewRemediationDetails,
}) => {
  // Check if there are any details to display
  const hasDetails = finding.details && finding.details.length > 0;

  // If no details, show empty state
  if (!hasDetails) {
    return (
      <div className="text-center py-8 text-dark-400" aria-live="polite">
        No detailed information available for this finding.
      </div>
    );
  }

  // Get the first detail to extract column headers
  const firstDetail = finding.details[0];

  // Get all keys except compliance and remediate for table headers
  const columnKeys = Object.keys(firstDetail).filter(
    (key) => key !== "compliance" && key !== "remediate"
  );

  // Check if compliance column should be displayed
  const showComplianceColumn = Object.prototype.hasOwnProperty.call(
    firstDetail,
    "compliance"
  );

  return (
    <>
      {/* Desktop Table View */}
      <div
        className="hidden md:block overflow-x-auto"
        role="region"
        aria-label="Finding details"
        tabIndex={0}
      >
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-dark-800">
              {/* Render column headers */}
              {columnKeys.map((key) => (
                <th
                  key={key}
                  className="text-left py-3 px-4 text-dark-300 font-medium text-sm border-b border-dark-700"
                  scope="col"
                >
                  {finding.field_labels[key] || key}
                </th>
              ))}

              {/* Add compliance header if needed */}
              {showComplianceColumn && (
                <th
                  key="compliance"
                  className="text-left py-3 px-4 text-dark-300 font-medium text-sm border-b border-dark-700 min-w-[200px]"
                  scope="col"
                >
                  {finding.field_labels["compliance"] || "Compliance Status"}
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {/* Render detail rows */}
            {finding.details.map((detail, index) => (
              <FindingDetailsRow
                key={index}
                detail={detail}
                index={index}
                fieldLabels={finding.field_labels}
                findingId={finding.id}
                onRemediate={onRemediate}
                remediatingDetailIndex={remediatingDetailIndex}
                isPending={isPending}
                onViewRemediationDetails={onViewRemediationDetails}
              />
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4">
        {finding.details.map((detail, index) => (
          <div
            key={index}
            className="bg-dark-850 rounded-lg border border-dark-700 p-4 space-y-3"
          >
            {/* Render all fields except compliance and remediate */}
            {Object.entries(detail).map(([key, value]) => {
              // Skip compliance and remediate fields as they're handled separately
              if (key === "compliance" || key === "remediate") return null;

              return (
                <div key={key} className="flex justify-between items-start">
                  <span className="text-dark-400 text-sm font-medium min-w-0 flex-shrink-0 mr-3">
                    {finding.field_labels[key] || key}:
                  </span>
                  <div className="text-dark-200 text-sm text-right min-w-0 flex-1">
                    {/* Render different data types appropriately */}
                    {typeof value === "boolean" ? (
                      <div className="flex justify-end">
                        <div
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            value
                              ? "bg-success-500/20 text-success-400"
                              : "bg-error-500/20 text-error-400"
                          }`}
                        >
                          {value ? "Yes" : "No"}
                        </div>
                      </div>
                    ) : typeof value === "object" && value !== null ? (
                      // Skip rendering objects directly
                      <span className="text-dark-400 italic">
                        {key === "remediate"
                          ? "[Remediation data]"
                          : "[Object data]"}
                      </span>
                    ) : (
                      String(value)
                    )}
                  </div>
                </div>
              );
            })}

            {/* Render compliance section if it exists */}
            {Object.prototype.hasOwnProperty.call(detail, "compliance") && (
              <div className="pt-3 border-t border-dark-700">
                <div className="flex items-center justify-between">
                  <span className="text-dark-400 text-sm font-medium">
                    {finding.field_labels["compliance"] || "Compliance Status"}:
                  </span>
                  <div className="flex items-center gap-2">
                    {/* Compliance status indicator */}
                    <div
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        detail.compliance === true
                          ? "bg-success-500/20 text-success-400"
                          : "bg-error-500/20 text-error-400"
                      }`}
                    >
                      {detail.compliance === true
                        ? "Compliant"
                        : "Non-Compliant"}
                    </div>

                    {/* Remediation button or status for mobile */}
                    {detail.compliance === false &&
                      !("remediate" in detail) && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onRemediate(finding.id, index, detail);
                          }}
                          disabled={
                            isPending && remediatingDetailIndex === index
                          }
                          className="px-3 py-1 bg-primary-600 hover:bg-primary-700 disabled:bg-dark-600 disabled:cursor-not-allowed text-white text-xs rounded-md transition-colors"
                        >
                          {isPending && remediatingDetailIndex === index
                            ? "Remediating..."
                            : "Remediate"}
                        </button>
                      )}
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </>
  );
};

export default React.memo(FindingDetailsTable);
