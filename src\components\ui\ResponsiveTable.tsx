import React from "react";
import { cn } from "@utils/cn";

interface Column<T> {
  key: keyof T | string;
  header: string;
  render?: (item: T, index: number) => React.ReactNode;
  className?: string;
  mobileLabel?: string; // Custom label for mobile card view
  hideOnMobile?: boolean; // Hide this column on mobile
  sortable?: boolean;
}

interface ResponsiveTableProps<T> {
  data: T[];
  columns: Column<T>[];
  className?: string;
  emptyMessage?: string;
  loading?: boolean;
  onRowClick?: (item: T, index: number) => void;
  mobileCardView?: boolean; // Force card view on mobile (default: true)
}

function ResponsiveTable<T extends Record<string, any>>({
  data,
  columns,
  className,
  emptyMessage = "No data available",
  loading = false,
  onRowClick,
  mobileCardView = true,
}: ResponsiveTableProps<T>) {
  if (loading) {
    return (
      <div className="space-y-4">
        {/* Desktop skeleton */}
        <div className="hidden md:block">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-dark-800">
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      className="text-left py-3 px-4 text-dark-400 font-medium text-sm"
                    >
                      <div className="h-4 bg-dark-700 rounded animate-pulse w-20"></div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {Array.from({ length: 5 }).map((_, index) => (
                  <tr key={index} className="border-b border-dark-800/50">
                    {columns.map((_, colIndex) => (
                      <td key={colIndex} className="py-3 px-4">
                        <div className="h-4 bg-dark-700 rounded animate-pulse w-full"></div>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile skeleton */}
        <div className="md:hidden space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="bg-dark-800 rounded-lg border border-dark-700 p-4"
            >
              <div className="space-y-3">
                <div className="h-4 bg-dark-700 rounded animate-pulse w-3/4"></div>
                <div className="h-3 bg-dark-700 rounded animate-pulse w-1/2"></div>
                <div className="h-3 bg-dark-700 rounded animate-pulse w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-8 text-dark-400">
        <p>{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      {/* Desktop Table View */}
      <div
        className={cn(
          "overflow-x-auto",
          mobileCardView ? "hidden md:block" : ""
        )}
      >
        <table className="w-full">
          <thead>
            <tr className="border-b border-dark-800">
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={cn(
                    "text-left py-3 px-4 text-dark-400 font-medium text-sm",
                    column.hideOnMobile && "hidden md:table-cell",
                    column.className
                  )}
                >
                  {column.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((item, rowIndex) => (
              <tr
                key={rowIndex}
                className={cn(
                  "border-b border-dark-800/50 transition-colors",
                  onRowClick && "hover:bg-dark-800/30 cursor-pointer"
                )}
                onClick={() => onRowClick?.(item, rowIndex)}
              >
                {columns.map((column, colIndex) => (
                  <td
                    key={colIndex}
                    className={cn(
                      "py-3 px-4 text-dark-200",
                      column.hideOnMobile && "hidden md:table-cell",
                      column.className
                    )}
                  >
                    {column.render
                      ? column.render(item, rowIndex)
                      : String(item[column.key] || "")}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      {mobileCardView && (
        <div className="md:hidden space-y-4">
          {data.map((item, index) => (
            <div
              key={index}
              className={cn(
                "bg-dark-800 rounded-lg border border-dark-700 p-4 transition-all",
                onRowClick &&
                  "hover:bg-dark-800/80 cursor-pointer active:scale-[0.98]"
              )}
              onClick={() => onRowClick?.(item, index)}
            >
              <div className="space-y-3">
                {columns
                  .filter((col) => !col.hideOnMobile)
                  .map((column, colIndex) => {
                    const value = column.render
                      ? column.render(item, index)
                      : String(item[column.key] || "");

                    if (!value) return null;

                    return (
                      <div
                        key={colIndex}
                        className="flex justify-between items-start"
                      >
                        <span className="text-dark-400 text-sm font-medium min-w-0 flex-shrink-0 mr-3">
                          {column.mobileLabel || column.header}:
                        </span>
                        <div className="text-dark-200 text-sm text-right min-w-0 flex-1">
                          {value}
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default ResponsiveTable;
