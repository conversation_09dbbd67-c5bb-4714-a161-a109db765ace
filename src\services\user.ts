import type {
    CreateUserRequest,
    CreateUserResponse,
    CreateCustomRoleRequest,
    CreateCustomRoleResponse,
    AssignRoleRequest,
    AssignRoleResponse,
    RevokeRoleRequest,
    RevokeRoleResponse,
    UpdateCustomRoleRequest,
    UpdateCustomRoleResponse,
    DeleteCustomRoleResponse,
    RolesResponse,
    UserPermissionsResponse,
    DeleteUserResponse,
    GetUsersResponse,
    TeamMembersResponse,
    UpdateTeamMemberRequest,
    UpdateTeamMemberResponse,
    ChangePasswordRequest,
    ChangePasswordResponse,
    UpdateUserInfoRequest,
    UpdateUserInfoResponse,
    UpdatePasswordRequest,
    UpdatePasswordResponse
} from '../types/api';
import { createBaseApi } from './baseService';

const userAPI = createBaseApi();

// User Management
export const getUsers = async (): Promise<GetUsersResponse> => {
    const response = await userAPI.get<GetUsersResponse>('/api/admin/users');
    return response.data;
};

export const createUser = async (data: CreateUserRequest): Promise<CreateUserResponse> => {
    const response = await userAPI.post<CreateUserResponse>('/api/admin/users', data);
    return response.data;
};

export const deleteUser = async (userId: number): Promise<DeleteUserResponse> => {
    const response = await userAPI.delete<DeleteUserResponse>('/api/admin/users', {
        params: { user_to_delete: userId }
    });
    return response.data;
};

// Role Management
export const createCustomRole = async (data: CreateCustomRoleRequest): Promise<CreateCustomRoleResponse> => {
    const response = await userAPI.post<CreateCustomRoleResponse>('/api/custom-roles', data);
    return response.data;
};

export const updateCustomRole = async (data: UpdateCustomRoleRequest): Promise<UpdateCustomRoleResponse> => {
    const response = await userAPI.put<UpdateCustomRoleResponse>('/api/custom-roles', data);
    return response.data;
};

export const deleteCustomRole = async (roleId: number): Promise<DeleteCustomRoleResponse> => {
    const response = await userAPI.delete<DeleteCustomRoleResponse>('/api/custom-roles', {
        params: { custom_role_id: roleId }
    });
    return response.data;
};

export const assignRole = async (data: AssignRoleRequest): Promise<AssignRoleResponse> => {
    const response = await userAPI.post<AssignRoleResponse>('/api/assign-role', data);
    return response.data;
};

export const revokeRole = async (data: RevokeRoleRequest): Promise<RevokeRoleResponse> => {
    const response = await userAPI.post<RevokeRoleResponse>('/api/revoke-role', data);
    return response.data;
};

export const getRoles = async (): Promise<RolesResponse> => {
    const response = await userAPI.get<RolesResponse>('/api/roles');
    return response.data;
};

export const getUserPermissions = async (): Promise<UserPermissionsResponse> => {
    const response = await userAPI.get<UserPermissionsResponse>('/api/user-permissions');
    return response.data;
};

/**
 * Get all team members in the workspace
 * @returns Promise with team members response
 */
export const getTeamMembers = async (): Promise<TeamMembersResponse> => {
    const response = await userAPI.get<TeamMembersResponse>('/api/team-members');
    return response.data;
};

/**
 * Update a team member's role
 * @param data The update team member request data
 * @returns Promise with update team member response
 */
export const updateTeamMember = async (data: UpdateTeamMemberRequest): Promise<UpdateTeamMemberResponse> => {
    const response = await userAPI.put<UpdateTeamMemberResponse>('/api/team-member', data);
    return response.data;
};

/**
 * Change a user's password
 * @param data The change password request data
 * @returns Promise with change password response
 */
export const changePassword = async (data: ChangePasswordRequest): Promise<ChangePasswordResponse> => {
    const response = await userAPI.post<ChangePasswordResponse>('/api/change-password', data);
    return response.data;
};

/**
 * Update user profile information
 * @param data The update user info request data
 * @returns Promise with update user info response
 */
export const updateUserInfo = async (data: UpdateUserInfoRequest): Promise<UpdateUserInfoResponse> => {
    const response = await userAPI.put<UpdateUserInfoResponse>('/api/update-user-info', data);
    return response.data;
};

/**
 * Update current user's password
 * @param data The update password request data
 * @returns Promise with update password response
 */
export const updatePassword = async (data: UpdatePasswordRequest): Promise<UpdatePasswordResponse> => {
    const response = await userAPI.post<UpdatePasswordResponse>('/api/update-password', data);
    return response.data;
};