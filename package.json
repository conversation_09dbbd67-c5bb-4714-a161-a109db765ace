{"name": "cloud-security-scanner", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "npm run validate:env:dev && vite", "build": "npm run validate:env:dev && vite build", "build:staging": "npm run validate:env:staging && vite build --mode staging", "build:production": "npm run validate:env:production && vite build --mode production", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:production": "vite preview --mode production", "type-check": "tsc --noEmit", "analyze": "vite-bundle-analyzer", "validate:env": "node scripts/validate-env.js", "validate:env:dev": "node scripts/validate-env.js development", "validate:env:staging": "node scripts/validate-env.js staging", "validate:env:production": "node scripts/validate-env.js production"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@tanstack/react-query": "^5.74.11", "@tanstack/react-query-devtools": "^5.74.11", "axios": "^1.11.0", "clsx": "^2.1.0", "framer-motion": "^11.0.8", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.2.1", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.15.3", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.4"}}