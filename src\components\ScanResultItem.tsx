import React from "react";
import { formatDate } from "@/lib/dateUtils";
import { Finding } from "@/types/api";
import { cn } from "../utils/cn";
import Badge from "./ui/Badge";
import { ChevronRight, Info } from "lucide-react";

interface ScanResultItemProps {
  finding: Finding;
  onClick: () => void;
}

const SeverityBadge: React.FC<{ severity: string }> = ({ severity }) => {
  const severityMap = {
    critical: { variant: "error" as const, text: "Critical" },
    high: { variant: "error" as const, text: "High" },
    medium: { variant: "warning" as const, text: "Medium" },
    low: { variant: "neutral" as const, text: "Low" },
  };

  const config =
    severityMap[severity as keyof typeof severityMap] || severityMap.low;

  return (
    <Badge variant={config.variant} size="sm">
      {config.text}
    </Badge>
  );
};

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const statusMap = {
    pass: { variant: "success" as const, text: "PASS" },
    fail: { variant: "error" as const, text: "FAIL" },
    remediated: { variant: "primary" as const, text: "REMEDIATED" },
  };

  const config = statusMap[status as keyof typeof statusMap] || statusMap.fail;

  return (
    <Badge variant={config.variant} size="sm">
      {config.text}
    </Badge>
  );
};

const ScanResultItem: React.FC<ScanResultItemProps> = ({
  finding,
  onClick,
}) => {
  return (
    <div
      className="group p-4 border border-dark-700 rounded-lg hover:border-primary-500/50 bg-dark-800 cursor-pointer transition-all duration-200 mb-3"
      onClick={onClick}
    >
      {/* Desktop Layout */}
      <div className="hidden md:grid grid-cols-12 gap-4 items-center">
        <div className="col-span-5">
          <div className="flex gap-2 items-center">
            <div
              className={cn(
                "p-1.5 rounded-full",
                finding.status === "pass"
                  ? "bg-success-500/20"
                  : "bg-error-500/20"
              )}
            >
              <Info
                size={16}
                className={
                  finding.status === "pass"
                    ? "text-success-400"
                    : "text-error-400"
                }
              />
            </div>
            <div>
              <h4 className="font-medium text-dark-200">
                {finding.description}
              </h4>
            </div>
          </div>
        </div>

        <div className="col-span-2">
          <SeverityBadge severity={finding.severity} />
        </div>

        <div className="col-span-2">
          <StatusBadge status={finding.status} />
        </div>

        <div className="col-span-2 text-sm text-dark-400">
          {formatDate(finding?.lastSeen)}
        </div>

        <div className="col-span-1 flex justify-end">
          <ChevronRight
            size={18}
            className="text-dark-500 group-hover:text-primary-500 transition-colors"
          />
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden space-y-3">
        {/* Header with icon and title */}
        <div className="flex items-start gap-3">
          <div
            className={cn(
              "p-1.5 rounded-full flex-shrink-0 mt-0.5",
              finding.status === "pass"
                ? "bg-success-500/20"
                : "bg-error-500/20"
            )}
          >
            <Info
              size={16}
              className={
                finding.status === "pass"
                  ? "text-success-400"
                  : "text-error-400"
              }
            />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-dark-200 leading-tight">
              {finding.description}
            </h4>
          </div>
          <ChevronRight
            size={18}
            className="text-dark-500 group-hover:text-primary-500 transition-colors flex-shrink-0 mt-1"
          />
        </div>

        {/* Badges and Date */}
        <div className="flex items-center justify-between flex-wrap gap-2">
          <div className="flex items-center gap-2">
            <SeverityBadge severity={finding.severity} />
            <StatusBadge status={finding.status} />
          </div>
          <div className="text-sm text-dark-400">
            {formatDate(finding?.lastSeen)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScanResultItem;
